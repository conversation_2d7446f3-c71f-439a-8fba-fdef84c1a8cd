// ========================================================================================
// MIDDLEWARE DE SANITIZAÇÃO PARA APIS - PROTEÇÃO EM TODAS AS REQUISIÇÕES
// ========================================================================================

import { inputSanitizer } from './inputSanitizer';
import { logger } from './logger';

interface ApiSanitizationConfig {
  sanitizeRequest: boolean;
  sanitizeResponse: boolean;
  logSanitization: boolean;
  maxPayloadSize: number;
  allowedMethods: string[];
}

interface SanitizedApiResponse<T = any> {
  data: T;
  sanitized: boolean;
  warnings: string[];
}

class ApiSanitizer {
  private config: ApiSanitizationConfig;
  private sensitiveEndpoints: string[];
  private bypassEndpoints: string[];

  constructor() {
    this.config = {
      sanitizeRequest: true,
      sanitizeResponse: false, // Responses do Supabase já são seguros
      logSanitization: true,
      maxPayloadSize: 10 * 1024 * 1024, // 10MB
      allowedMethods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']
    };

    this.sensitiveEndpoints = [
      '/auth/',
      '/entregadores',
      '/empresas',
      '/agendamentos',
      '/billing/',
      '/admin/'
    ];

    this.bypassEndpoints = [
      '/health',
      '/status',
      '/metrics'
    ];
  }

  /**
   * Sanitiza payload de requisição
   */
  sanitizeRequestPayload(payload: any, endpoint: string): {
    sanitized: any;
    warnings: string[];
  } {
    if (!this.shouldSanitize(endpoint)) {
      return { sanitized: payload, warnings: [] };
    }

    const warnings: string[] = [];
    
    try {
      // Verificar tamanho do payload
      const payloadSize = JSON.stringify(payload).length;
      if (payloadSize > this.config.maxPayloadSize) {
        warnings.push(`Payload muito grande: ${payloadSize} bytes`);
        logger.warn('Payload excede tamanho máximo', { 
          endpoint, 
          size: payloadSize,
          maxSize: this.config.maxPayloadSize 
        });
      }

      const sanitized = this.deepSanitizeObject(payload, warnings);
      
      if (this.config.logSanitization && warnings.length > 0) {
        logger.warn('Payload sanitizado', { 
          endpoint, 
          warnings: warnings.length,
          originalSize: payloadSize 
        });
      }

      return { sanitized, warnings };
      
    } catch (error) {
      logger.error('Erro na sanitização do payload', { endpoint, error });
      return { sanitized: payload, warnings: ['Erro na sanitização'] };
    }
  }

  /**
   * Sanitiza objeto recursivamente
   */
  private deepSanitizeObject(obj: any, warnings: string[]): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (typeof obj === 'string') {
      const original = obj;
      const sanitized = inputSanitizer.sanitizeString(obj);
      
      if (original !== sanitized) {
        warnings.push(`String sanitizada: ${original.length} -> ${sanitized.length} chars`);
      }
      
      return sanitized;
    }

    if (typeof obj === 'number' || typeof obj === 'boolean') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepSanitizeObject(item, warnings));
    }

    if (typeof obj === 'object') {
      const sanitized: any = {};
      
      for (const [key, value] of Object.entries(obj)) {
        // Sanitizar a chave também
        const sanitizedKey = inputSanitizer.sanitizeString(key, {
          allowedChars: /[a-zA-Z0-9_-]/,
          maxLength: 100
        });
        
        if (key !== sanitizedKey) {
          warnings.push(`Chave sanitizada: ${key} -> ${sanitizedKey}`);
        }
        
        // Sanitizar o valor
        sanitized[sanitizedKey] = this.deepSanitizeObject(value, warnings);
      }
      
      return sanitized;
    }

    return obj;
  }

  /**
   * Verifica se endpoint deve ser sanitizado
   */
  private shouldSanitize(endpoint: string): boolean {
    // Bypass para endpoints específicos
    if (this.bypassEndpoints.some(bypass => endpoint.includes(bypass))) {
      return false;
    }

    // Sanitizar endpoints sensíveis
    return this.sensitiveEndpoints.some(sensitive => endpoint.includes(sensitive));
  }

  /**
   * Intercepta requisições do Supabase
   */
  interceptSupabaseRequest(originalFetch: typeof fetch) {
    return async (url: string | URL | Request, options?: RequestInit): Promise<Response> => {
      const urlString = url.toString();
      
      // Verificar se é requisição para Supabase
      if (!urlString.includes('supabase.co')) {
        return originalFetch(url, options);
      }

      let sanitizedOptions = options;
      
      // Sanitizar body se existir
      if (options?.body && this.config.sanitizeRequest) {
        try {
          const bodyData = JSON.parse(options.body.toString());
          const { sanitized, warnings } = this.sanitizeRequestPayload(bodyData, urlString);
          
          if (warnings.length > 0) {
            logger.warn('Requisição Supabase sanitizada', { 
              url: urlString.replace(/https:\/\/[^\/]+/, '[SUPABASE_URL]'),
              warnings: warnings.length 
            });
          }
          
          sanitizedOptions = {
            ...options,
            body: JSON.stringify(sanitized)
          };
          
        } catch (error) {
          // Se não conseguir parsear, deixa como está
          logger.debug('Não foi possível parsear body da requisição', { error });
        }
      }

      return originalFetch(url, sanitizedOptions);
    };
  }

  /**
   * Wrapper para funções de API
   */
  wrapApiFunction<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    endpoint: string
  ): T {
    return (async (...args: any[]) => {
      try {
        // Sanitizar argumentos se necessário
        let sanitizedArgs = args;
        
        if (this.config.sanitizeRequest && args.length > 0) {
          const { sanitized, warnings } = this.sanitizeRequestPayload(args[0], endpoint);
          
          if (warnings.length > 0) {
            logger.warn('Argumentos de API sanitizados', { 
              endpoint, 
              warnings: warnings.length 
            });
          }
          
          sanitizedArgs = [sanitized, ...args.slice(1)];
        }

        // Executar função original
        const result = await fn(...sanitizedArgs);
        
        return result;
        
      } catch (error) {
        logger.error('Erro na execução de API sanitizada', { endpoint, error });
        throw error;
      }
    }) as T;
  }

  /**
   * Sanitiza parâmetros de URL
   */
  sanitizeUrlParams(params: Record<string, any>): Record<string, string> {
    const sanitized: Record<string, string> = {};
    
    for (const [key, value] of Object.entries(params)) {
      const sanitizedKey = inputSanitizer.sanitizeString(key, {
        allowedChars: /[a-zA-Z0-9_-]/,
        maxLength: 50
      });
      
      const sanitizedValue = inputSanitizer.sanitizeString(value?.toString() || '', {
        maxLength: 500
      });
      
      sanitized[sanitizedKey] = sanitizedValue;
    }
    
    return sanitized;
  }

  /**
   * Valida headers de requisição
   */
  validateHeaders(headers: Record<string, string>): {
    valid: boolean;
    sanitized: Record<string, string>;
    warnings: string[];
  } {
    const warnings: string[] = [];
    const sanitized: Record<string, string> = {};
    let valid = true;

    for (const [key, value] of Object.entries(headers)) {
      const lowerKey = key.toLowerCase();
      
      // Headers perigosos
      if (lowerKey.includes('script') || lowerKey.includes('eval')) {
        warnings.push(`Header perigoso removido: ${key}`);
        valid = false;
        continue;
      }
      
      // Sanitizar valor do header
      const sanitizedValue = inputSanitizer.sanitizeString(value, {
        maxLength: 1000,
        removeSpecialChars: true
      });
      
      if (value !== sanitizedValue) {
        warnings.push(`Header sanitizado: ${key}`);
      }
      
      sanitized[key] = sanitizedValue;
    }

    return { valid, sanitized, warnings };
  }

  /**
   * Configurar sanitização
   */
  configure(config: Partial<ApiSanitizationConfig>) {
    this.config = { ...this.config, ...config };
    
    logger.info('ApiSanitizer configurado', { config: this.config });
  }
}

// Instância singleton
export const apiSanitizer = new ApiSanitizer();

// Função para inicializar interceptação global
export function initializeApiSanitization() {
  // Interceptar fetch global
  const originalFetch = window.fetch;
  window.fetch = apiSanitizer.interceptSupabaseRequest(originalFetch);
  
  logger.info('Sanitização de API inicializada globalmente');
}

// Decorator para métodos de API
export function sanitizeApi(endpoint: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    descriptor.value = apiSanitizer.wrapApiFunction(method, endpoint);
  };
}

// Hook para React
export function useApiSanitization() {
  return {
    sanitizePayload: apiSanitizer.sanitizeRequestPayload.bind(apiSanitizer),
    sanitizeUrlParams: apiSanitizer.sanitizeUrlParams.bind(apiSanitizer),
    validateHeaders: apiSanitizer.validateHeaders.bind(apiSanitizer)
  };
}
