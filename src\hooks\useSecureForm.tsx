// ========================================================================================
// HOOK PARA FORMULÁRIOS SEGUROS COM SANITIZAÇÃO AUTOMÁTICA
// ========================================================================================

import { useState, useCallback, useEffect } from 'react';
import { inputSanitizer } from '@/lib/inputSanitizer';
import { logger } from '@/lib/logger';

interface ValidationRule {
  field: string;
  type: 'email' | 'cpf' | 'cnpj' | 'phone' | 'text' | 'number' | 'password';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: string) => string | null;
}

interface UseSecureFormOptions {
  initialData?: Record<string, any>;
  validationRules: ValidationRule[];
  sanitizeOnChange?: boolean;
  sanitizeOnSubmit?: boolean;
  logSanitization?: boolean;
}

interface FormState {
  data: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  sanitizationWarnings: string[];
}

export function useSecureForm(options: UseSecureFormOptions) {
  const {
    initialData = {},
    validationRules,
    sanitizeOnChange = true,
    sanitizeOnSubmit = true,
    logSanitization = true
  } = options;

  const [formState, setFormState] = useState<FormState>({
    data: initialData,
    errors: {},
    touched: {},
    isValid: false,
    isSubmitting: false,
    sanitizationWarnings: []
  });

  // Validar um campo específico
  const validateField = useCallback((fieldName: string, value: any): string | null => {
    const rule = validationRules.find(r => r.field === fieldName);
    if (!rule) return null;

    const stringValue = value?.toString() || '';

    // Campo obrigatório
    if (rule.required && !stringValue.trim()) {
      return `${fieldName} é obrigatório`;
    }

    // Tamanho mínimo
    if (rule.minLength && stringValue.length < rule.minLength) {
      return `${fieldName} deve ter pelo menos ${rule.minLength} caracteres`;
    }

    // Tamanho máximo
    if (rule.maxLength && stringValue.length > rule.maxLength) {
      return `${fieldName} deve ter no máximo ${rule.maxLength} caracteres`;
    }

    // Padrão regex
    if (rule.pattern && stringValue && !rule.pattern.test(stringValue)) {
      return `${fieldName} tem formato inválido`;
    }

    // Validador customizado
    if (rule.customValidator) {
      return rule.customValidator(stringValue);
    }

    return null;
  }, [validationRules]);

  // Sanitizar um valor específico
  const sanitizeValue = useCallback((fieldName: string, value: any): string => {
    const rule = validationRules.find(r => r.field === fieldName);
    if (!rule || !value) return value?.toString() || '';

    const originalValue = value.toString();
    let sanitizedValue: string;

    switch (rule.type) {
      case 'email':
        sanitizedValue = inputSanitizer.sanitizeEmail(originalValue);
        break;
      case 'cpf':
        sanitizedValue = inputSanitizer.sanitizeCPF(originalValue);
        break;
      case 'cnpj':
        sanitizedValue = inputSanitizer.sanitizeCNPJ(originalValue);
        break;
      case 'phone':
        sanitizedValue = inputSanitizer.sanitizePhone(originalValue);
        break;
      case 'password':
        // Senhas não são sanitizadas, apenas validadas
        sanitizedValue = originalValue;
        break;
      default:
        sanitizedValue = inputSanitizer.sanitizeString(originalValue, {
          maxLength: rule.maxLength || 1000
        });
    }

    // Log se houve mudança na sanitização
    if (logSanitization && originalValue !== sanitizedValue) {
      logger.warn('Valor sanitizado detectado', {
        field: fieldName,
        originalLength: originalValue.length,
        sanitizedLength: sanitizedValue.length,
        type: rule.type
      });
    }

    return sanitizedValue;
  }, [validationRules, logSanitization]);

  // Atualizar um campo
  const updateField = useCallback((fieldName: string, value: any) => {
    setFormState(prev => {
      const sanitizedValue = sanitizeOnChange ? sanitizeValue(fieldName, value) : value;
      const error = validateField(fieldName, sanitizedValue);
      
      const newData = { ...prev.data, [fieldName]: sanitizedValue };
      const newErrors = { ...prev.errors };
      const newTouched = { ...prev.touched, [fieldName]: true };

      if (error) {
        newErrors[fieldName] = error;
      } else {
        delete newErrors[fieldName];
      }

      // Verificar se o formulário é válido
      const isValid = Object.keys(newErrors).length === 0 && 
                     validationRules.filter(r => r.required).every(r => newData[r.field]);

      return {
        ...prev,
        data: newData,
        errors: newErrors,
        touched: newTouched,
        isValid
      };
    });
  }, [sanitizeOnChange, sanitizeValue, validateField, validationRules]);

  // Validar todo o formulário
  const validateForm = useCallback(() => {
    const errors: Record<string, string> = {};
    
    validationRules.forEach(rule => {
      const value = formState.data[rule.field];
      const error = validateField(rule.field, value);
      if (error) {
        errors[rule.field] = error;
      }
    });

    setFormState(prev => ({
      ...prev,
      errors,
      isValid: Object.keys(errors).length === 0
    }));

    return Object.keys(errors).length === 0;
  }, [formState.data, validateField, validationRules]);

  // Sanitizar todo o formulário
  const sanitizeForm = useCallback(() => {
    const sanitizedData: Record<string, any> = {};
    const warnings: string[] = [];

    validationRules.forEach(rule => {
      const originalValue = formState.data[rule.field];
      const sanitizedValue = sanitizeValue(rule.field, originalValue);
      
      sanitizedData[rule.field] = sanitizedValue;
      
      if (originalValue !== sanitizedValue) {
        warnings.push(`Campo ${rule.field} foi sanitizado`);
      }
    });

    setFormState(prev => ({
      ...prev,
      data: sanitizedData,
      sanitizationWarnings: warnings
    }));

    return sanitizedData;
  }, [formState.data, sanitizeValue, validationRules]);

  // Submeter formulário com sanitização
  const submitForm = useCallback(async (onSubmit: (data: Record<string, any>) => Promise<void>) => {
    setFormState(prev => ({ ...prev, isSubmitting: true }));

    try {
      // Sanitizar se necessário
      const dataToSubmit = sanitizeOnSubmit ? sanitizeForm() : formState.data;
      
      // Validar
      const isValid = validateForm();
      if (!isValid) {
        throw new Error('Formulário contém erros de validação');
      }

      // Log da submissão
      logger.info('Formulário submetido com segurança', {
        fieldsCount: Object.keys(dataToSubmit).length,
        sanitized: sanitizeOnSubmit,
        warnings: formState.sanitizationWarnings.length
      });

      await onSubmit(dataToSubmit);
      
    } catch (error) {
      logger.error('Erro na submissão do formulário', { error });
      throw error;
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [sanitizeOnSubmit, sanitizeForm, formState.data, formState.sanitizationWarnings, validateForm]);

  // Reset do formulário
  const resetForm = useCallback(() => {
    setFormState({
      data: initialData,
      errors: {},
      touched: {},
      isValid: false,
      isSubmitting: false,
      sanitizationWarnings: []
    });
  }, [initialData]);

  // Marcar campo como tocado
  const touchField = useCallback((fieldName: string) => {
    setFormState(prev => ({
      ...prev,
      touched: { ...prev.touched, [fieldName]: true }
    }));
  }, []);

  // Verificar se campo tem erro e foi tocado
  const getFieldError = useCallback((fieldName: string): string | null => {
    return formState.touched[fieldName] ? formState.errors[fieldName] || null : null;
  }, [formState.touched, formState.errors]);

  // Verificar se campo é válido
  const isFieldValid = useCallback((fieldName: string): boolean => {
    return !formState.errors[fieldName];
  }, [formState.errors]);

  // Effect para validação inicial
  useEffect(() => {
    validateForm();
  }, []);

  return {
    // Estado
    data: formState.data,
    errors: formState.errors,
    touched: formState.touched,
    isValid: formState.isValid,
    isSubmitting: formState.isSubmitting,
    sanitizationWarnings: formState.sanitizationWarnings,
    
    // Métodos
    updateField,
    validateForm,
    sanitizeForm,
    submitForm,
    resetForm,
    touchField,
    getFieldError,
    isFieldValid,
    
    // Utilitários
    sanitizeValue,
    validateField
  };
}

// Hook simplificado para casos comuns
export function useSecureLoginForm() {
  return useSecureForm({
    validationRules: [
      {
        field: 'email',
        type: 'email',
        required: true,
        maxLength: 254
      },
      {
        field: 'password',
        type: 'password',
        required: true,
        minLength: 6,
        maxLength: 128
      }
    ],
    sanitizeOnChange: true,
    sanitizeOnSubmit: true
  });
}

// Hook para cadastro de entregador
export function useSecureEntregadorForm() {
  return useSecureForm({
    validationRules: [
      {
        field: 'nome',
        type: 'text',
        required: true,
        minLength: 2,
        maxLength: 100
      },
      {
        field: 'email',
        type: 'email',
        required: true,
        maxLength: 254
      },
      {
        field: 'cpf',
        type: 'cpf',
        required: true
      },
      {
        field: 'telefone',
        type: 'phone',
        required: false
      }
    ],
    sanitizeOnChange: true,
    sanitizeOnSubmit: true
  });
}
