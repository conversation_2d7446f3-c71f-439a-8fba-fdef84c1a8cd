// ========================================================================================
// FORMULÁRIO DE LOGIN SEGURO - EXEMPLO DE USO DO HOOK useSecureForm
// ========================================================================================

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Shield, AlertTriangle, Eye, EyeOff } from "lucide-react";
import { useSecureLoginForm } from "@/hooks/useSecureForm";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";

interface SecureLoginFormProps {
  onSuccess?: () => void;
}

export function SecureLoginForm({ onSuccess }: SecureLoginFormProps) {
  const { signIn } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  
  const {
    data,
    errors,
    isValid,
    isSubmitting,
    sanitizationWarnings,
    updateField,
    submitForm,
    getFieldError,
    touchField
  } = useSecureLoginForm();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await submitForm(async (sanitizedData) => {
        const { error } = await signIn(sanitizedData.email, sanitizedData.password);
        
        if (error) {
          throw new Error('Credenciais inválidas');
        }
        
        toast.success('Login realizado com sucesso!');
        onSuccess?.();
      });
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Erro no login');
    }
  };

  const emailError = getFieldError('email');
  const passwordError = getFieldError('password');

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Shield className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Login Seguro</h2>
        </div>
        <p className="text-sm text-gray-600">
          Formulário com sanitização automática de dados
        </p>
      </div>

      {/* Avisos de Sanitização */}
      {sanitizationWarnings.length > 0 && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            <strong>Dados sanitizados:</strong>
            <ul className="mt-1 list-disc list-inside text-sm">
              {sanitizationWarnings.map((warning, index) => (
                <li key={index}>{warning}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Formulário */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Campo Email */}
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={data.email || ''}
            onChange={(e) => updateField('email', e.target.value)}
            onBlur={() => touchField('email')}
            className={emailError ? 'border-red-500' : ''}
            disabled={isSubmitting}
          />
          {emailError && (
            <p className="text-sm text-red-600">{emailError}</p>
          )}
          <p className="text-xs text-gray-500">
            ✓ Sanitização automática contra XSS
          </p>
        </div>

        {/* Campo Senha */}
        <div className="space-y-2">
          <Label htmlFor="password">Senha</Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder="Sua senha"
              value={data.password || ''}
              onChange={(e) => updateField('password', e.target.value)}
              onBlur={() => touchField('password')}
              className={passwordError ? 'border-red-500 pr-10' : 'pr-10'}
              disabled={isSubmitting}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isSubmitting}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </Button>
          </div>
          {passwordError && (
            <p className="text-sm text-red-600">{passwordError}</p>
          )}
          <p className="text-xs text-gray-500">
            ✓ Validação de força e tamanho
          </p>
        </div>

        {/* Botão Submit */}
        <Button
          type="submit"
          className="w-full"
          disabled={!isValid || isSubmitting}
        >
          {isSubmitting ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Entrando...</span>
            </div>
          ) : (
            'Entrar'
          )}
        </Button>

        {/* Status de Validação */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${isValid ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className={isValid ? 'text-green-600' : 'text-red-600'}>
              {isValid ? 'Formulário válido' : 'Preencha todos os campos'}
            </span>
          </div>
        </div>
      </form>

      {/* Informações de Segurança */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-semibold text-blue-800 mb-2">
          🔒 Recursos de Segurança Ativos:
        </h3>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>✓ Sanitização automática contra XSS</li>
          <li>✓ Proteção contra SQL Injection</li>
          <li>✓ Validação de formato de email</li>
          <li>✓ Verificação de força da senha</li>
          <li>✓ Logs sanitizados em produção</li>
          <li>✓ Interceptação de requisições maliciosas</li>
        </ul>
      </div>

      {/* Debug Info (apenas em desenvolvimento) */}
      {process.env.NODE_ENV === 'development' && (
        <details className="bg-gray-50 border rounded-lg p-4">
          <summary className="text-sm font-semibold cursor-pointer">
            🔧 Debug Info (Dev Only)
          </summary>
          <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-auto">
            {JSON.stringify({
              data,
              errors,
              isValid,
              sanitizationWarnings
            }, null, 2)}
          </pre>
        </details>
      )}
    </div>
  );
}

// Exemplo de uso em uma página
export function SecureLoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <SecureLoginForm 
        onSuccess={() => {
          // Redirecionar após login bem-sucedido
          window.location.href = '/dashboard';
        }}
      />
    </div>
  );
}
