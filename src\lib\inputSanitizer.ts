// ========================================================================================
// SISTEMA DE SANITIZAÇÃO DE INPUTS - PROTEÇÃO CONTRA XSS E INJECTION
// ========================================================================================

import { logger } from './logger';

interface SanitizationConfig {
  allowHtml: boolean;
  maxLength: number;
  trimWhitespace: boolean;
  removeSpecialChars: boolean;
  allowedChars?: RegExp;
}

interface ValidationRule {
  field: string;
  type: 'email' | 'cpf' | 'cnpj' | 'phone' | 'text' | 'number' | 'password';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  sanitize?: boolean;
}

class InputSanitizer {
  private xssPatterns: RegExp[];
  private sqlInjectionPatterns: RegExp[];
  private sensitivePatterns: RegExp[];

  constructor() {
    this.xssPatterns = [
      // Scripts maliciosos
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
      
      // Event handlers
      /on\w+\s*=\s*["'][^"']*["']/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /data:text\/html/gi,
      
      // Meta tags perigosos
      /<meta\b[^>]*>/gi,
      /<link\b[^>]*>/gi,
      /<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi
    ];

    this.sqlInjectionPatterns = [
      // SQL keywords perigosos
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /(\b(OR|AND)\s+['"]?\w+['"]?\s*=\s*['"]?\w+['"]?)/gi,
      
      // Caracteres de escape SQL
      /[';\\x00\\n\\r\\x1a]/gi,
      /(\-\-|\#|\/\*|\*\/)/gi,
      
      // Tentativas de bypass
      /(\bUNION\b.*\bSELECT\b)/gi,
      /(\bINTO\s+OUTFILE\b)/gi
    ];

    this.sensitivePatterns = [
      // Tentativas de path traversal
      /\.\.[\/\\]/gi,
      /[\/\\]etc[\/\\]/gi,
      /[\/\\]proc[\/\\]/gi,
      /[\/\\]sys[\/\\]/gi,
      
      // Comandos do sistema
      /(\b(cmd|powershell|bash|sh|exec|eval|system)\b)/gi,
      
      // Protocolos perigosos
      /^(file|ftp|gopher|ldap|dict):/gi
    ];
  }

  /**
   * Sanitiza string removendo caracteres perigosos
   */
  sanitizeString(input: string, config: Partial<SanitizationConfig> = {}): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    const defaultConfig: SanitizationConfig = {
      allowHtml: false,
      maxLength: 1000,
      trimWhitespace: true,
      removeSpecialChars: false,
      ...config
    };

    let sanitized = input;

    // 1. Trim whitespace
    if (defaultConfig.trimWhitespace) {
      sanitized = sanitized.trim();
    }

    // 2. Verificar tamanho máximo
    if (sanitized.length > defaultConfig.maxLength) {
      logger.warn('Input excede tamanho máximo', { 
        length: sanitized.length, 
        maxLength: defaultConfig.maxLength 
      });
      sanitized = sanitized.substring(0, defaultConfig.maxLength);
    }

    // 3. Remover XSS
    if (!defaultConfig.allowHtml) {
      sanitized = this.removeXSS(sanitized);
    }

    // 4. Remover SQL Injection
    sanitized = this.removeSQLInjection(sanitized);

    // 5. Remover path traversal e comandos
    sanitized = this.removeSensitivePatterns(sanitized);

    // 6. Aplicar filtro de caracteres permitidos
    if (defaultConfig.allowedChars) {
      sanitized = sanitized.replace(new RegExp(`[^${defaultConfig.allowedChars.source}]`, 'g'), '');
    }

    // 7. Remover caracteres especiais se solicitado
    if (defaultConfig.removeSpecialChars) {
      sanitized = sanitized.replace(/[<>'"&]/g, '');
    }

    return sanitized;
  }

  /**
   * Remove padrões XSS
   */
  private removeXSS(input: string): string {
    let cleaned = input;
    
    this.xssPatterns.forEach(pattern => {
      const matches = cleaned.match(pattern);
      if (matches) {
        logger.warn('Tentativa de XSS detectada e removida', { 
          pattern: pattern.source,
          matches: matches.length 
        });
        cleaned = cleaned.replace(pattern, '');
      }
    });

    // Encode caracteres HTML perigosos
    cleaned = cleaned
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');

    return cleaned;
  }

  /**
   * Remove padrões de SQL Injection
   */
  private removeSQLInjection(input: string): string {
    let cleaned = input;
    
    this.sqlInjectionPatterns.forEach(pattern => {
      const matches = cleaned.match(pattern);
      if (matches) {
        logger.warn('Tentativa de SQL Injection detectada e removida', { 
          pattern: pattern.source,
          matches: matches.length 
        });
        cleaned = cleaned.replace(pattern, '');
      }
    });

    return cleaned;
  }

  /**
   * Remove padrões sensíveis (path traversal, comandos)
   */
  private removeSensitivePatterns(input: string): string {
    let cleaned = input;
    
    this.sensitivePatterns.forEach(pattern => {
      const matches = cleaned.match(pattern);
      if (matches) {
        logger.warn('Padrão sensível detectado e removido', { 
          pattern: pattern.source,
          matches: matches.length 
        });
        cleaned = cleaned.replace(pattern, '');
      }
    });

    return cleaned;
  }

  /**
   * Sanitiza CPF
   */
  sanitizeCPF(cpf: string): string {
    if (!cpf) return '';
    
    // Remove tudo exceto números
    const cleaned = cpf.replace(/\D/g, '');
    
    // Verifica se tem 11 dígitos
    if (cleaned.length !== 11) {
      logger.warn('CPF com formato inválido', { length: cleaned.length });
      return '';
    }

    return cleaned;
  }

  /**
   * Sanitiza CNPJ
   */
  sanitizeCNPJ(cnpj: string): string {
    if (!cnpj) return '';
    
    // Remove tudo exceto números
    const cleaned = cnpj.replace(/\D/g, '');
    
    // Verifica se tem 14 dígitos
    if (cleaned.length !== 14) {
      logger.warn('CNPJ com formato inválido', { length: cleaned.length });
      return '';
    }

    return cleaned;
  }

  /**
   * Sanitiza email
   */
  sanitizeEmail(email: string): string {
    if (!email) return '';
    
    const cleaned = this.sanitizeString(email, {
      maxLength: 254,
      allowedChars: /[a-zA-Z0-9@._-]/
    }).toLowerCase();

    // Validação básica de formato
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(cleaned)) {
      logger.warn('Email com formato inválido', { email: cleaned });
      return '';
    }

    return cleaned;
  }

  /**
   * Sanitiza telefone
   */
  sanitizePhone(phone: string): string {
    if (!phone) return '';
    
    // Remove tudo exceto números
    const cleaned = phone.replace(/\D/g, '');
    
    // Verifica se tem entre 10 e 11 dígitos (Brasil)
    if (cleaned.length < 10 || cleaned.length > 11) {
      logger.warn('Telefone com formato inválido', { length: cleaned.length });
      return '';
    }

    return cleaned;
  }

  /**
   * Valida e sanitiza múltiplos campos
   */
  validateAndSanitizeForm(data: Record<string, any>, rules: ValidationRule[]): {
    sanitized: Record<string, any>;
    errors: string[];
  } {
    const sanitized: Record<string, any> = {};
    const errors: string[] = [];

    rules.forEach(rule => {
      const value = data[rule.field];
      
      // Verificar se é obrigatório
      if (rule.required && (!value || value.toString().trim() === '')) {
        errors.push(`${rule.field} é obrigatório`);
        return;
      }

      if (!value) {
        sanitized[rule.field] = '';
        return;
      }

      let sanitizedValue: string;

      // Aplicar sanitização específica por tipo
      switch (rule.type) {
        case 'email':
          sanitizedValue = this.sanitizeEmail(value.toString());
          break;
        case 'cpf':
          sanitizedValue = this.sanitizeCPF(value.toString());
          break;
        case 'cnpj':
          sanitizedValue = this.sanitizeCNPJ(value.toString());
          break;
        case 'phone':
          sanitizedValue = this.sanitizePhone(value.toString());
          break;
        case 'password':
          // Senhas não devem ser sanitizadas, apenas validadas
          sanitizedValue = value.toString();
          break;
        default:
          sanitizedValue = this.sanitizeString(value.toString(), {
            maxLength: rule.maxLength || 1000
          });
      }

      // Validações adicionais
      if (rule.minLength && sanitizedValue.length < rule.minLength) {
        errors.push(`${rule.field} deve ter pelo menos ${rule.minLength} caracteres`);
      }

      if (rule.maxLength && sanitizedValue.length > rule.maxLength) {
        errors.push(`${rule.field} deve ter no máximo ${rule.maxLength} caracteres`);
      }

      if (rule.pattern && !rule.pattern.test(sanitizedValue)) {
        errors.push(`${rule.field} tem formato inválido`);
      }

      sanitized[rule.field] = sanitizedValue;
    });

    return { sanitized, errors };
  }
}

// Instância singleton
export const inputSanitizer = new InputSanitizer();

// Hooks para React
export function useSanitizedInput() {
  return {
    sanitizeString: inputSanitizer.sanitizeString.bind(inputSanitizer),
    sanitizeCPF: inputSanitizer.sanitizeCPF.bind(inputSanitizer),
    sanitizeCNPJ: inputSanitizer.sanitizeCNPJ.bind(inputSanitizer),
    sanitizeEmail: inputSanitizer.sanitizeEmail.bind(inputSanitizer),
    sanitizePhone: inputSanitizer.sanitizePhone.bind(inputSanitizer),
    validateForm: inputSanitizer.validateAndSanitizeForm.bind(inputSanitizer)
  };
}
